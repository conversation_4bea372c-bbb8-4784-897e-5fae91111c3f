#!/bin/bash

echo "🚀 Setting up Humans as Tools Async Agent..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 20+ first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Add missing chalk dependency
echo "🎨 Adding chalk dependency..."
npm install chalk

# Generate BAML client
echo "🤖 Generating BAML client..."
npx baml-cli generate

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  Creating .env file from template..."
    cp .env.example .env
    echo ""
    echo "🔑 IMPORTANT: Please edit the .env file and add your API keys:"
    echo "   - OPENAI_API_KEY (required)"
    echo "   - ANTHROPIC_API_KEY (optional)"
    echo ""
    echo "   You can get an OpenAI API key from: https://platform.openai.com/api-keys"
    echo ""
else
    echo "✅ .env file already exists"
fi

echo ""
echo "🎉 Setup complete! You can now run the agent:"
echo ""
echo "   CLI mode:    npm run dev"
echo "   Server mode: npx tsx src/server.ts"
echo ""
echo "📖 Example commands to try:"
echo "   'multiply 5 and 7'"
echo "   'process a refund for order 12345 for $50 because item was damaged'"
echo "   'divide 10 by 2' (requires human approval)"
echo ""
